{{--
    This Blade file renders a preview of the SPH document.
    Location: resources/views/sph/sph-preview.blade.php
--}}

<!-- include -->
@php 
    use App\Support\Formatter;
@endphp

<!-- logic -->
@php 
    // var
    $locale = 'id';
    $letterSetting = $record->letterSetting;

    // --- UPDATED: The query now specifically fetches only the two required ISOs ---
    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();


    // SIGNATURE SET PART
    $signer = null;
    
    // First, check if there is a final, approved record.
    $finalApproval = $record->approvedApprovals; // This uses the HasOne relationship

    if ($finalApproval) {
        // If an approved record exists, the signer is the user who approved it.
        $signer = $finalApproval->user;
    } else {
        // If not yet approved, find the default manager from the notification settings.
        $eventName = 'sph_manager_update_sales'; // The event name for the SPH manager
        $defaultApproverSetting = \App\Models\NotificationSetting::findActiveRecipientsForEvent($eventName)->first();
        
        if ($defaultApproverSetting) {
            $signer = $defaultApproverSetting->user;
        } 
    }
@endphp

<div class="p-4 sm:p-6 bg-white font-sans text-gray-800">

    {{-- Header Section --}}
    <header class="mb-8">
        <table class="w-full">
            <tbody>
                <tr>
                    {{-- Column 1: Logo (Left) --}}
                    <td class="w-1/3">
                        <img src="{{ asset('images/lrp.png') }}" alt="PT. Lintas Riau Prima" style="height: 150px;"
                            class="mb-2">
                    </td>
                    {{-- Column 2: Empty Spacer (Center) --}}
                    <td class="w-1/3"></td>
                    {{-- Column 3: Partner Text (Right) --}}
                    <td class="w-1/3 text-right">
                        <h2 class="font-bold text-lg">TRUSTED & RELIABLE PARTNER</h2>
                        <p class="text-xs">Fuel Agent – Fuel Transportation – Bunker Service</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </header>

    {{-- Document Info Section --}}
    <section class="mb-4">
        <div class="text-right text-sm font-semibold">
            {{ $letterSetting?->city }}, {{ Formatter::date($record->sph_date, 'id') }}
        </div>

        <table class="text-sm mt-4">
            <tbody>
                <tr>
                    <td class="pr-2">No</td>
                    <td class="pr-2">:</td>
                    <td class="font-semibold">{{ $record->sph_number }}</td>
                </tr>
                <tr>
                    <td class="pr-2">Lampiran</td>
                    <td class="pr-2">:</td>
                    <td>-</td>
                </tr>
                <tr>
                    <td class="pr-2 align-top">Perihal</td>
                    <td class="pr-2 align-top">:</td>
                    <td class="font-semibold">Penawaran Harga Pertalite Industri<br>Pertamina Patra Niaga</td>
                </tr>
            </tbody>
        </table>
    </section>

    {{-- Recipient Section --}}
    <section class="mb-4 text-sm font-semibold">
        <p>Kepada Yth.</p>
        <p class="font-bold">{{ $record->customer?->nama }}</p>
        @if ($record->opsional_pic)
            <p>Up: {{ $record->opsional_pic }}</p>
        @elseif($record->customer?->pic_nama)
            <p>Up: {{ $record->customer->pic_nama }}</p>
        @endif
        <p>Di –</p>
        <p class="ml-4">Tempat</p>
    </section>

    {{-- Body / Salutation --}}
    <section class="mb-6 text-sm leading-relaxed">
        <p class="mb-0">Salam hormat,</p>
        <p>
            Sehubungan dengan adanya informasi kebutuhan BBM Pertalite industri untuk PT. Amico Putera Perkasa, maka
            bersama ini kami kirimkan surat penawaran harga untuk periode
            <span class="font-bold">{{ $record->sph_date->format('d M Y') }}</span> s/d <span
                class="font-bold">{{ $record->valid_until_date->format('d M Y') }}</span>.
        </p>
    </section>

    {{-- Price Details Table --}}
    <section class="mb-2">
        <h2 class="text-sm font-semibold mb-2">Harga Penawaran yang kami berikan yaitu:</h2>
        <table class="w-full text-left text-sm border-collapse border border-gray-400">
            <thead>
                <tr class="bg-gray-100">
                    <th class="p-2 border border-gray-300 text-center">No</th>
                    <th class="p-2 border border-gray-300">Rincian</th>
                    <th class="p-2 border border-gray-300 text-right">Harga/Liter</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($record->details as $idx => $detail)
                    <!-- harga dasar -->
                    <tr class="border-t border-gray-300">
                        <td class="p-2 border border-gray-300 text-center">{{ $idx + 1 }}</td>
                        <td class="p-2 border border-gray-300">Dasar BBM</td>
                        <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->harga_dasar, $locale) }}</td>
                    </tr>

                    <!-- ppn/vat -->
                    @if ($detail->show_ppn)
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">PPN BBM 11%</td>
                            <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->ppn, $locale) }}</td>
                        </tr>
                    @endif

                    <!-- oat -->
                    @if ($detail->show_oat)
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">OAT</td>
                            <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->oat, $locale) }}</td>
                        </tr>
                    @endif

                    <!-- pbbkb -->
                    @if ($detail->show_pbbkb)
                        <tr>
                            <td class="p-2 border border-gray-300 text-center"></td>
                            <td class="p-2 border border-gray-300">PBBKB</td>
                            <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->pbbkb, $locale) }}</td>
                        </tr>
                    @endif

                    <!-- total price -->
                    <tr class="font-bold bg-gray-100">
                        <td colspan="2" class="p-2 border border-gray-300">Total Penawaran</td>
                        <td class="p-2 border border-gray-300 text-right">{{ Formatter::currency($detail->price, $locale) }}</td>
                    </tr>

                @endforeach
            </tbody>
        </table>
    </section>

    {{-- Terms and Conditions --}}
    <section class="mb-8 text-sm">
        <h2 class="font-semibold">Syarat dan Ketentuan</h2>
        <ol class="list-none space-y-0 mb-2">
            <li class="flex"><span class="mr-2">1.</span><span>Penawaran harga ini berlaku pada periode
                    {{ $record->sph_date->format('d M Y') }} -
                    {{ $record->valid_until_date->format('d M Y') }}.</span></li>
            <li class="flex"><span class="mr-2">2.</span>
                <div><span>Pembayaran tagihan CASH hari setelah Dokumen diterima melalui transfer ke Bank BNI</span>
                    <div class="font-semibold ml-4">No Rekening: ********* An. PT. Lintas Riau Prima</div>
                </div>
            </li>
            <li class="flex"><span class="mr-2">3.</span><span>PO kami terima minimal 3 (Tiga) hari (via email atau
                    WA) sebelum pengiriman.</span></li>
            <li class="flex"><span class="mr-2">4.</span><span>Untuk kondisi mendesak/urgent dapat berkoordinasi
                    langsung sebelum pukul 12.00 Wib. pada hari yang sama.</span></li>
        </ol>
        <span>Demikian surat penawaran ini kami sampaikan dan atas perhatian kami ucapkan terima kasih yang sebesar-besarnya</span>
    </section>

    {{-- Signature Section --}}
    <section class="pt-8 flex justify-end mt-6 mb-6">
        <div class="text-center">
            <p class="text-sm">Hormat kami</p>
            <p class="text-sm">PT Lintas Riau Prima</p>
            
            @if($signer)
                {{-- The signature mounter will now show the correct person's signature --}}
                <x-signature-mounter :user="$signer" />

                {{-- The name and position will also be from the correct person --}}
                <p class="text-sm font-bold underline">{{ $signer->name ?? 'Tanpa Nama' }}</p>
                <p class="text-xs text-gray-600">{{ $signer->getPosition() ?? 'Tanpa Posisi' }}</p>
            @else
                {{-- Fallback content if no signer could be determined --}}
                <div style="height: 100px;"></div>
                <p class="text-sm font-bold underline">No Data</p>
                <p class="text-xs text-gray-600">No Data</p>
            @endif
        </div>
    </section>

    {{-- Footer Section --}}
    <footer class="mt-16 pt-4 border-t-4 border-blue-800 flex justify-between items-center text-xs">
        <div class="flex items-center space-x-2">
            @if (isset($isoCertifications))
                @foreach ($isoCertifications as $cert)
                    <img src="{{ $cert->logo_url }}" alt="{{ $cert->name }}" class="h-10">
                @endforeach
            @endif
        </div>
        <div class="text-center">
            <p class="font-bold">PT. LINTAS RIAU PRIMA</p>
            <p>Jl. Mesjid Al Furqon No. 26</p>
            <p>Pekanbaru, Riau. 28144</p>
        </div>
        <div class="text-left">
            <p>☎️ 0761-22369</p>
            <p>✉️ <EMAIL></p>
            <p>🌐 www.lintasriauprima.com</p>
        </div>
    </footer>
</div>
