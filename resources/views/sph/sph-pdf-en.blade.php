<!-- include -->
@php 
    use App\Support\Formatter;
@endphp

<!-- logic -->
@php 
    $locale = 'en';

    $letterSetting = $record->letterSetting;

    $isoNamesToDisplay = ['ISO 9001:2015', 'ISO 45001:2018'];
    $isoCertifications = \App\Models\IsoCertification::where('is_active', true)
        ->whereIn('name', $isoNamesToDisplay)
        ->get();

    $signer = null;
    $finalApproval = $record->approvedApprovals;

    if ($finalApproval) {
        $signer = $finalApproval->user;
    } else {
        $eventName = 'sph_manager_update_sales';
        $defaultApproverSetting = \App\Models\NotificationSetting::findActiveRecipientsForEvent($eventName)->first();
        if ($defaultApproverSetting) {
            $signer = $defaultApproverSetting->user;
        } 
    }
@endphp

<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Quotation - {{ $record->sph_number }}</title>
        <style>
            body { font-family: Arial, sans-serif; font-size: 11px; line-height: 1.2; color: #333; margin: 0; padding: 20px; }
            .header { margin-bottom: 8px; }
            .header table { width: 100%; }
            .header td { vertical-align: top; }
            .logo { height: 100px; max-width: 180px; object-fit: contain; }
            .partner-text { text-align: right; }
            .partner-text h2 { font-size: 14px; font-weight: bold; margin: 0; }
            .partner-text p { font-size: 9px; margin: 4px 0 0 0; }
            .doc-info { margin-bottom: 8px; }
            .doc-info .date { text-align: right; margin-bottom: 8px; }
            .doc-info table { font-size: 11px; }
            .doc-info td { padding: 1px 0; }
            .recipient { margin-bottom: 8px; font-size: 11px; }
            .body-text { margin-bottom: 8px; font-size: 11px; line-height: 1.3; }
            .price-table { width: 100%; border-collapse: collapse; margin-bottom: 8px; }
            .price-table th, .price-table td { border: 1px solid #333; padding: 5px; text-align: left; }
            .price-table th { background-color: #f0f0f0; font-weight: bold; text-align: center; }
            .price-table .number { text-align: center; }
            .price-table .amount { text-align: right; }
            .price-table .total-row { background-color: #f0f0f0; font-weight: bold; }
            .terms { margin-bottom: 8px; font-size: 11px; }
            .terms h2 { font-size: 12px; margin-bottom: 6px; }
            .terms ol { padding-left: 0; list-style: none; margin: 0; }
            .terms li { margin-bottom: 4px; padding: 0; display: block; }
            .terms li span:first-child { margin-right: 8px; min-width: 15px; display: inline-block; vertical-align: top; }
            .signature { margin-top: 10px; text-align: right; }
            .signature-box { display: inline-block; text-align: center; }
            .signature-space { height: 40px; margin: 5px 0; }
            
            /* --- UPDATED: This CSS makes the footer sticky --- */
            .footer { 
                position: fixed; 
                bottom: 0px; 
                left: 20px; 
                right: 20px; 
                height: 50px; /* Adjust height as needed */
                padding-top: 8px; 
                border-top: 4px solid #1e40af; 
                font-size: 9px; 
            }
            
            .footer table { width: 100%; }
            .footer td { vertical-align: top; }
            .footer .center { text-align: center; }
            .footer .right { text-align: left; }
            .iso-logos { text-align: left; }
            .iso-logos img { height: 35px; margin-right: 10px; }

            /* This ensures the main content doesn't run into the footer */
            main {
                padding-bottom: 60px; /* Should be slightly more than footer height */
            }
        </style>
    </head>

    <body>
        <main>
            {{-- Header Section --}}
            <div class="header">
                <table>
                    <tr>
                        <td style="width: 33%;">
                            @php $logoPath = public_path('images/lrp.png'); @endphp
                            @if (file_exists($logoPath))
                                <img src="data:image/png;base64,{{ base64_encode(file_get_contents($logoPath)) }}" alt="PT. Lintas Riau Prima" class="logo">
                            @endif
                        </td>
                        <td style="width: 34%;"></td>
                        <td style="width: 33%;" class="partner-text">
                            <h2>TRUSTED & RELIABLE PARTNER</h2>
                            <p>Fuel Agent – Fuel Transportation – Bunker Service</p>
                        </td>
                    </tr>
                </table>
            </div>

            {{-- Document Info Section --}}
            <div class="doc-info">
                <div class="date">
                    {{ $letterSetting?->city }}, {{ Formatter::date($record->sph_date, $locale) }}
                </div>
                <table>
                    <tr>
                        <td style="width: 80px;">No.</td>
                        <td style="width: 10px;">:</td>
                        <td><strong>{{ $record->sph_number }}</strong></td>
                    </tr>
                    <tr>
                        <td>Attachment</td>
                        <td>:</td>
                        <td>-</td>
                    </tr>
                    <tr>
                        <td style="vertical-align: top;">Subject</td>
                        <td style="vertical-align: top;">:</td>
                        <td><strong>Price Quotation for Industrial Pertalite<br>Pertamina Patra Niaga</strong></td>
                    </tr>
                </table>
            </div>

            {{-- Recipient Section --}}
            <div class="recipient">
                <p>To:</p>
                <p><strong>{{ $record->customer?->nama }}</strong></p>
                @if ($record->opsional_pic)
                    <p>Attn: {{ $record->opsional_pic }}</p>
                @elseif($record->customer?->pic_nama)
                    <p>Attn: {{ $record->customer->pic_nama }}</p>
                @endif
                <p>At your location</p>
            </div>

            {{-- Body / Salutation --}}
            <div class="body-text">
                <p style="margin-bottom: 10px;">Dear Sir/Madam,</p>
                <p>
                    In connection with the information on fuel needs for {{ $record->customer?->nama }},
                    we hereby send a price quotation for the period
                    <strong>{{ Formatter::date($record->sph_date, $locale) }}</strong> to
                    <strong>{{ Formatter::date($record->valid_until_date, $locale) }}</strong>.
                </p>
            </div>

            {{-- Price Details Table --}}
            <div>
                <h2 style="font-size: 13px; margin-bottom: 8px;">The price quotation we provide is as follows:</h2>
                <table class="price-table">
                    <thead>
                        <tr>
                            <th style="width: 50px;">No.</th>
                            <th>Details</th>
                            <th style="width: 120px;">Price/Liter</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($record->details as $idx => $detail)
                            <tr>
                                <td class="number">{{ $idx + 1 }}</td>
                                <td>Base Fuel Price</td>
                                <td class="amount">{{ Formatter::currency($detail->harga_dasar, $locale) }}</td>
                            </tr>
                            @if ($detail->show_ppn)
                                <tr>
                                    <td class="number"></td>
                                    <td>VAT 11%</td>
                                    <td class="amount">{{ Formatter::currency($detail->ppn, $locale) }}</td>
                                </tr>
                            @endif
                            @if ($detail->show_oat)
                                <tr>
                                    <td class="number"></td>
                                    <td>Delivery Cost</td>
                                    <td class="amount">{{ Formatter::currency($detail->oat, $locale) }}</td>
                                </tr>
                            @endif
                            @if ($detail->show_pbbkb)
                                <tr>
                                    <td class="number"></td>
                                    <td>PBBKB</td>
                                    <td class="amount">{{ Formatter::currency($detail->pbbkb, $locale) }}</td>
                                </tr>
                            @endif
                            <tr class="total-row">
                                <td colspan="2"><strong>Total Quotation</strong></td>
                                <td class="amount"><strong>{{ Formatter::currency($detail->price, $locale) }}</strong></td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            {{-- Terms and Conditions --}}
            <div class="terms" style="margin-bottom: 6px;">
                <h2>Terms and Conditions</h2>

                <table style="width: 100%; font-size: 11px; margin-bottom: 8px;">
                    <tbody>
                        <tr>
                            <td style="width: 20px; vertical-align: top;">1.</td>
                            <td>This price quotation is valid for the period {{ Formatter::date($record->sph_date, $locale) }} - {{ Formatter::date($record->valid_until_date, $locale) }}.</td>
                        </tr>
                        <tr>
                            <td style="vertical-align: top;">2.</td>
                            <td>Payment of CASH invoice after documents are received via transfer to <strong>Bank BNI Account No: ********* On behalf of PT. Lintas Riau Prima</strong>.</td>
                        </tr>
                        <tr>
                            <td style="vertical-align: top;">3.</td>
                            <td>We receive the PO at least 3 (three) days (via email or WA) before delivery.</td>
                        </tr>
                        <tr>
                            <td style="vertical-align: top;">4.</td>
                            <td>For urgent conditions, please coordinate directly before 12:00 PM on the same day.</td>
                        </tr>
                    </tbody>
                </table>

                <span>Thus we convey this offer letter, and for your attention, we thank you very much.</span>
            </div>

            {{-- Signature Section --}}
            <div class="signature">
                <div class="signature-box">
                    <p>Sincerely,</p>
                    <p>PT Lintas Riau Prima</p>

                    <!-- signature mounter -->
                    @include('components.signature-mounter-pdf', ['signer' => $signer])

                    <p><strong><u>{{ $signer?->name ?? 'N/A' }}</u></strong></p>
                    <p style="font-size: 10px;">{{ $signer?->getPosition($locale) ?? 'Marketing Manager' }}</p>
                </div>
            </div>
        </main>

        {{-- Footer Section --}}
        <div class="footer">
            <table>
                <tr>
                    <td style="width: 33%;" class="iso-logos">
                        @foreach ($isoCertifications as $cert)
                            @php $logoPath = public_path('storage/' . $cert->logo_path); @endphp
                            @if (file_exists($logoPath))
                                <img src="data:image/png;base64,{{ base64_encode(file_get_contents($logoPath)) }}" alt="{{ $cert->name }}" style="height: 40px; margin-right: 10px;">
                            @endif
                        @endforeach
                    </td>
                    <td style="width: 34%;" class="center">
                        <p><strong>PT. LINTAS RIAU PRIMA</strong></p>
                        <p>{{ $letterSetting?->address }}</p>
                    </td>
                    <td style="width: 33%;" class="right">
                        <p>Tel: 0761-22369</p>
                        <p>Email: <EMAIL></p>
                        <p>Web: www.lintasriauprima.com</p>
                    </td>
                </tr>
            </table>
        </div>
    </body>

</html>
